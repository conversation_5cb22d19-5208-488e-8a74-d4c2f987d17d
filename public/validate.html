<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>AI Agent 门户验证页</title>
  <style>
    body {
      font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
      margin: 24px;
    }

    .demo {
      max-width: 800px;
      line-height: 1.6;
    }

    .card {
      padding: 16px 20px;
      border: 1px solid #eee;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
    }

    .spacer {
      height: 80vh;
    }
  </style>
</head>

<body>
  <div class="demo">
    <h1>AI Agent 门户验证页</h1>
    <div class="card">
      <p>预期行为：</p>
      <ul>
        <li>页面右下角显示一个悬浮的 AI 图标</li>
        <li>点击图标，屏幕右侧滑出 100% 高度的门户对话框</li>
        <li>对话框内 iframe 宽高 100%，加载 Dify 应用</li>
      </ul>
      <p>提示：当前门户主页的 Dify URL 已在 .env 中配置（VITE_DIFY_URL）。</p>
    </div>
    <div class="spacer"></div>
    <p>滚动以确保悬浮按钮与面板定位正常。</p>
  </div>

  <!-- 引入已由 Vite 提供的嵌入脚本（开发环境下 /public 下的文件会以根路径提供） -->
  <script src="http://************:5173/embed.js"></script>
  <script>
    window.addEventListener('DOMContentLoaded', function () {
      window.AIAgentPortal.init({
        // 指向门户主页（即本工程根路径）。生产环境请替换为部署域名：例如 https://portal.example.com/
        portalBaseUrl: window.location.origin + '/',
        // 如需覆盖 .env 中的 Dify URL，可传入 difyUrl 字段，例如：
        // 最终url：http://************:9009/chatbot/4SxxkaznsPosK7Yy?sys.user_id=H4sIAAAAAAAA/zO0NDEyMTQ2MTY1MjcxsjQ2MjUAACRGvRoTAAAA&aaa=H4sIAAAAAAAACstIzcnJL88vyklRBACmy/U2CwAAAA==
        appUrl: 'http://************:9009/chatbot/4SxxkaznsPosK7Yy',
        meta:{
          'sys.user_id': '100001',
          aaa: 'user token'
        },
        autoOpen: false,
        onOpen: function(){
          console.log("ai agent open")
        },
        onClose: function(){
          console.log("ai agent close")
        }
      });
    });
  </script>
  <noscript>请启用 JavaScript 以使用 AI Agent 门户。</noscript>
</body>

</html>