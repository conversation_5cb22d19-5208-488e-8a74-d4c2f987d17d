<template>
  <div class="app-container">
    <div class="iframe-container" :style="{ width: iframeWidth + 'px' }">
      <iframe
        class="dify-frame"
        :src="resolvedUrl"
        frameborder="0"
        allow="clipboard-write; microphone; camera;"
      ></iframe>
    </div>
    <div
      class="resize-handle"
      @mousedown="startResize"
    ></div>
    <div class="sidebar">
      <div class="width-control">
        <label>宽度: {{ iframeWidth }}px</label>
        <input
          type="range"
          :min="minWidth"
          :max="maxWidth"
          v-model="iframeWidth"
          class="width-slider"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'

function getQueryParam(name) {
  const url = new URL(window.location.href)
  return url.searchParams.get(name)
}

// 优先读取 URL query: ?difyUrl=...，否则读取环境变量 VITE_DIFY_URL
const resolvedUrl = computed(() => {
  const q = getQueryParam('difyUrl')
  return q || import.meta.env.VITE_DIFY_URL || ''
})

// iframe 宽度控制
const iframeWidth = ref(800)
const minWidth = 300
const maxWidth = ref(1200)
const isResizing = ref(false)

// 初始化最大宽度
onMounted(() => {
  maxWidth.value = window.innerWidth - 200 // 留出侧边栏空间
  // 从 localStorage 恢复宽度设置
  const savedWidth = localStorage.getItem('iframe-width')
  if (savedWidth) {
    iframeWidth.value = Math.min(Math.max(parseInt(savedWidth), minWidth), maxWidth.value)
  }
})

// 保存宽度设置
const saveWidth = () => {
  localStorage.setItem('iframe-width', iframeWidth.value.toString())
}

// 拖拽调整宽度
const startResize = (e) => {
  isResizing.value = true
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  e.preventDefault()
}

const handleResize = (e) => {
  if (!isResizing.value) return

  const newWidth = e.clientX
  iframeWidth.value = Math.min(Math.max(newWidth, minWidth), maxWidth.value)
}

const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  saveWidth()
}

// 监听窗口大小变化
const handleWindowResize = () => {
  maxWidth.value = window.innerWidth - 200
  if (iframeWidth.value > maxWidth.value) {
    iframeWidth.value = maxWidth.value
  }
}

onMounted(() => {
  window.addEventListener('resize', handleWindowResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize)
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
})

// 监听宽度变化并保存
import { watch } from 'vue'
watch(iframeWidth, saveWidth)
</script>

<style scoped>
.app-container {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: stretch;
  overflow: hidden;
  background-color: #f5f5f5;
}

.iframe-container {
  height: 100%;
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.dify-frame {
  border: none;
  width: 100%;
  height: 100%;
}

.resize-handle {
  width: 8px;
  height: 100%;
  background: #e0e0e0;
  cursor: col-resize;
  position: relative;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background: #bdbdbd;
}

.resize-handle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3px;
  height: 30px;
  background: #9e9e9e;
  border-radius: 2px;
}

.sidebar {
  flex: 1;
  padding: 20px;
  background: #fafafa;
  min-width: 200px;
}

.width-control {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.width-control label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.width-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e0e0e0;
  outline: none;
  -webkit-appearance: none;
}

.width-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #2196f3;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.width-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #2196f3;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>

